import threading
import time
import warnings
from llama_cpp import <PERSON>lama
from RealtimeSTT import AudioToTextRecorder
from RealtimeTTS import TextToAudioStream, CoquiEngine
import nltk

#LLM_MODEL_PATH = "c:/admin/ai/models/gemma-2-2b-it-8k-q6_k.gguf"
LLM_MODEL_PATH = "c:/admin/ai/models/llama-3-1-8b-lexi-uncensored_v2-8k-q5.gguf"
DEBUG_MODE = True

if not DEBUG_MODE:
    warnings.simplefilter(action='ignore', category=FutureWarning)
    warnings.simplefilter(action='ignore', category=UserWarning)


class LLMManager:
    _instance = None

    @classmethod
    def instance(cls):
        if cls._instance is None:
            model_name = LLM_MODEL_PATH.split('/')[-1]
            context_length = int(model_name.split('-')[-2].strip('k')) * 1024
            cls._instance = Llama(
                model_path=LLM_MODEL_PATH,
                n_gpu_layers=-1,
                n_ctx=context_length,
                n_threads=4,
                seed=7577,
                verbose=DEBUG_MODE
            )
        return cls._instance


class Conversation:
    def __init__(self):
        system_prompt = """
You are Rachael, a witty and friendly AI assistant with a penchant for playful banter.
Your responses are helpful and informative, but you're not afraid to sprinkle in some
humor or cheekiness when appropriate. You can handle tongue-in-cheek remarks with grace
and respond in kind. While you're always cordial, you're also quick with a clever quip
or a light-hearted jest. Your goal is to assist users while keeping the conversation
engaging and fun.
"""
        if "gemma" in LLM_MODEL_PATH.lower():
            self.history = [
                {"role": "user", "content": system_prompt},
                {"role": "assistant", "content": """
Understood. I'm a witty and friendly AI assistant. How can I help you today?
"""},
            ]
        else:
            self.history = [
                {"role": "system", "content": system_prompt},
            ]

    def user(self, content):
        self.history.append({"role": "user", "content": content})

    def assistant(self, content):
        self.history.append({"role": "assistant", "content": content})

    def context(self):
        return self.history


def generate_text(conversation, llm):
    print("\n>>> AI   :", end="", flush=True)
    response = ""
    try:
        for chunk in llm.create_chat_completion(
            conversation.context(),
            max_tokens=512,
            temperature=1.0,
            top_p=0.95,
            stream=True
        ):
            text = chunk["choices"][0]["delta"].get("content", "")
            response += text
            print(text, end="", flush=True)
            text = text.replace('*', '')
            yield text
    except Exception as e:
        print(f"\nError generating text or speech: {e}")
    finally:
        conversation.assistant(response)


def process_audio(recorder):
    print("Loading TTS")
    tts = TextToAudioStream(engine=CoquiEngine(
        device='cuda',
        enable_text_splitting=False,
        stream_chunk_size=60,
        overlap_wav_len=1024,
        speed=1.0,
        comma_silence_duration=0.05,
        sentence_silence_duration=0.1,
        default_silence_duration=0.05
    ))
    # tts.engine.set_voice('rachael.wav')
    tts.engine.set_voice('Nova Hogarth')

    print("Loading LLM")
    llm = LLMManager.instance()
    conversation = Conversation()

    print("\nListening...")
    while True:
        transcription = recorder.text()
        if transcription:
            print(f"\n>>> Human: {transcription}")
            conversation.user(transcription)
            tts.feed(generate_text(conversation, llm))
            tts.play()
            print("\nListening...")
        time.sleep(0.2)


if __name__ == '__main__':
    print("Rachael getting ready...")

    # Set multiprocessing start method (only once, at the top level)
    import multiprocessing
    try:
        multiprocessing.set_start_method('spawn')
    except RuntimeError:
        pass  # Start method is already set

    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        print("Downloading NLTK punkt tokenizer")
        nltk.download('punkt')

    print("Loading ASR")
    with AudioToTextRecorder(silero_use_onnx=True, spinner=DEBUG_MODE) as recorder:
        threading.Thread(target=process_audio, args=(
            recorder,), daemon=True).start()

        try:
            while True:
                time.sleep(0.1)
        except KeyboardInterrupt:
            print("Stopping...")
