# Rachael - Real-time AI Voice Chat Assistant

## 1. System Overview

This document outlines the architecture, program flow, and implementation roadmap for <PERSON><PERSON><PERSON>; an offline, real-time AI voice chat assistant. The system aims to provide a natural, interrupt-driven conversation experience between a human user and an AI assistant.

### 1.1 Key Features

- Real-time speech recognition and response generation
- Voice activity detection
- Dynamic, interruptible AI responses
- Prioritization of human input
- Natural handling of conversation flow, including pauses and topic changes
- Parallel thought management through context stacking
- Intonation and emotion recognition
- Emotional response generation
- Speaker diarization
- Continuous personalized, and task specific learning

### 1.2 Current Tech Stack

- Automatic Speech Recognition (ASR): RealtimeSTT
- LLM engine: Llama-cpp-python
- Text-to-Speech (TTS): RealtimeTTS
- Speaker diarization: WhoSpeaks
- Audio I/O: Sounddevice

## 2. System Architecture

### 2.1 Core Components

1. **Audio Input Manager**

   - Handles continuous audio recording
   - Implements dynamic duration adjustment

2. **Speech Recognition Engine**

   - Implements streaming recognition for real-time processing

3. **Command Preprocessor**

   - Quickly identifies and processes meta-commands (stop, pause, resume)
   - Operates in parallel with main speech recognition

4. **Natural Language Understanding (NLU) Module**

   - Analyzes transcribed speech for intent and context
   - Feeds into the Context Manager

5. **Context Manager**

   - Maintains conversation state and history
   - Implements stack-based context switching

6. **Language Model Interface**

   - Handles incremental response generation

7. **Response Formulator**

   - Processes LLM output into coherent, interruptible segments
   - Manages response adaptation based on interrupts

8. **Text-to-Speech Engine**

   - Implements checkpointing for pause/resume functionality

9. **State Machine**

   - Manages overall conversation flow and system states
   - Handles rapid state transitions for interrupts

10. **Event System**

    - Manages prioritized events across all components
    - Ensures immediate handling of human input

11. **User Interface**
    - Provides visual feedback on system state (optional)
    - Displays transcriptions and AI responses (optional)

### 2.2 Data Flow

```
[Audio Input] -> [Speech Recognition] -> [Command Preprocessor] -|
                                                                 v
[Event System] <-> [State Machine] <-> [Context Manager] <-> [NLU Module]
    ^                   ^                    ^                  ^
    |                   |                    |                  |
    v                   v                    v                  v
[TTS Engine] <- [Response Formulator] <- [Language Model] <- [LLM Interface]
```

## 3. Program Flow

### 3.1 Initialization

1. Initialize all components
2. Enter "Awaiting_Human_Input" state

### 3.2 Main Loop

1. Continuously record audio
2. Process audio through Speech Recognition Engine
3. Check for commands in Command Preprocessor
4. If command detected, immediately process
5. Otherwise, pass transcription to NLU Module
6. Update Context Manager
7. Generate AI response through LLM Interface
8. Formulate response in segments
9. Begin TTS output while continuing to listen for interrupts

### 3.3 Interrupt Handling

1. Detect interrupt (new speech input or command)
2. Immediately pause or stop current TTS output
3. Process interrupt (command or new input)
4. Update system state
5. Either resume previous output or begin new response generation

## 4. State Machine

### 4.1 States

- Awaiting_Human_Input
- Processing_Human_Input
- AI_Thinking
- AI_Speaking_Interruptible
- AI_Paused
- Changing_Topic

### 4.2 Key Transitions

- Awaiting_Human_Input -> Processing_Human_Input (on speech detected)
- Processing_Human_Input -> AI_Thinking (on speech end)
- AI_Thinking -> AI_Speaking_Interruptible (on response ready)
- Any State -> AI_Paused (on pause command)
- AI_Paused -> AI_Speaking_Interruptible (on resume command)
- Any State -> Changing_Topic (on topic change or interrupt)

## 5. Implementation Roadmap

### Phase 1: Core Functionality

1. Set up project structure and environment
2. Implement Audio Input Manager with dynamic duration
3. Integrate streaming STT
4. Develop basic Command Preprocessor
5. Implement skeleton State Machine and Event System
6. Create simple Context Manager
7. Integrate LLM engine
8. Implement basic Response Formulator
9. Research and integrate faster TTS engine
10. Develop basic UI for system state visualization

### Phase 2: Enhanced Interactivity

1. Improve Command Preprocessor for faster recognition
2. Enhance Context Manager for better topic tracking
3. Implement incremental response generation in LLM Interface
4. Develop advanced Response Formulator with interruption handling
5. Implement checkpointing in TTS Engine
6. Enhance State Machine for more granular states
7. Improve Event System priority handling

### Phase 3: Natural Language Understanding

1. Develop NLU Module for intent recognition
2. Enhance Context Manager with NLU insights
3. Implement context-based response adaptation
4. Fine-tune LLM for more contextually appropriate responses

### Phase 4: Optimization and Polish

1. Optimize audio processing for lower latency
2. Implement advanced error handling and recovery
3. Fine-tune interrupt handling for more natural conversation flow
4. Optimize resource usage across all components
5. Enhance UI with more detailed conversation visualization
6. Conduct thorough testing and bug fixing
7. Implement conversation logging and analysis tools

### Phase 5: Advanced Features

1. Implement multi-turn context understanding
2. Develop personality layers for AI responses
3. Add support for multiple languages
4. Implement voice activity detection for more natural turn-taking
5. Explore emotion recognition in speech for more empathetic responses

## 6. Testing Strategy

1. Unit tests for each component
2. Integration tests for component interactions
3. End-to-end tests for full conversation flows
4. Stress tests for interrupt handling and long conversations
5. User acceptance testing with various conversation scenarios

## 7. Performance Considerations

1. Monitor and optimize latency in audio processing pipeline
2. Benchmark and improve LLM response generation time
3. Optimize TTS for faster and smoother output
4. Implement efficient memory management for long conversations
5. Consider parallel processing for applicable components

## 8. Future Enhancements

1. Integration with external knowledge bases
2. Support for multi-modal inputs (text, images)
3. Personalization based on user preferences and history
4. Integration with smart home or IoT devices
5. Development of mobile applications for on-the-go use

## 9. Conclusion

This architecture provides a comprehensive framework for developing a real-time AI voice chat assistant. By following this roadmap and continuously iterating based on testing and user feedback, we can create a highly responsive and natural conversational AI system.

## 10. Installation Notes

- Create a virtual environment

  ```bash
  pip install virtualenvwrapper-win
  set WORKON_HOME=%USERPROFILE%\envs
  mkvirtualenv rachael
  workon rachael
  ```

- Install required packages

  ```bash
  pip install -r requirements.txt
  ```

- Install PyTorch with CUDA 12.4 support

  ```bash
  pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124
  ```

- Install Llama-cpp-python with CUDA 12.4 support

  ```bash
  pip install llama-cpp-python --extra-index-url https://abetlen.github.io/llama-cpp-python/whl/cu124 --no-cache-dir --force-reinstall --upgrade
  ```
