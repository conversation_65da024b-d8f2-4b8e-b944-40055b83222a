name: rachael
channels:
  - conda-forge
  - nvidia
  - pytorch
dependencies:
  - blas=1.0
  - brotli-python=1.1.0
  - bzip2=1.0.8
  - ca-certificates=2025.7.9
  - cffi=1.17.1
  - cuda-cccl=12.9.27
  - cuda-cccl_win-64=12.9.27
  - cuda-cudart=12.4.127
  - cuda-cudart-dev=12.4.127
  - cuda-cupti=12.4.127
  - cuda-libraries=12.4.1
  - cuda-libraries-dev=12.4.1
  - cuda-nvrtc=12.4.127
  - cuda-nvrtc-dev=12.4.127
  - cuda-nvtx=12.4.127
  - cuda-opencl=12.9.19
  - cuda-opencl-dev=12.9.19
  - cuda-profiler-api=12.9.79
  - cuda-runtime=12.4.1
  - cuda-version=12.9
  - freetype=2.13.3
  - h2=4.2.0
  - hpack=4.1.0
  - hyperframe=6.1.0
  - intel-openmp=2025.2.0
  - khronos-opencl-icd-loader=2024.10.24
  - lcms2=2.17
  - lerc=4.0.0
  - libblas=3.9.0
  - libcblas=3.9.0
  - libcublas=********
  - libcublas-dev=********
  - libcufft=********
  - libcufft-dev=********
  - libcurand=**********
  - libcurand-dev=**********
  - libcusolver=********
  - libcusolver-dev=********
  - libcusparse=**********
  - libcusparse-dev=**********
  - libdeflate=1.24
  - libexpat=2.7.0
  - libffi=3.4.6
  - libfreetype=2.13.3
  - libfreetype6=2.13.3
  - libhwloc=2.11.2
  - libiconv=1.18
  - libjpeg-turbo=3.1.0
  - liblapack=3.9.0
  - liblzma=5.8.1
  - libnpp=12.2.5.30
  - libnpp-dev=12.2.5.30
  - libnvfatbin=12.9.82
  - libnvfatbin-dev=12.9.82
  - libnvjitlink=12.4.127
  - libnvjitlink-dev=12.4.127
  - libnvjpeg=**********
  - libnvjpeg-dev=**********
  - libpng=1.6.50
  - libsqlite=3.50.2
  - libtiff=4.7.0
  - libuv=1.51.0
  - libwebp=1.6.0
  - libwebp-base=1.6.0
  - libxcb=1.16
  - libxml2=2.13.8
  - libzlib=1.3.1
  - m2w64-gcc-libgfortran=5.3.0
  - m2w64-gcc-libs=5.3.0
  - m2w64-gcc-libs-core=5.3.0
  - m2w64-gmp=6.1.0
  - m2w64-libwinpthread-git=5.0.0.4634.697f757
  - mkl=2023.1.0
  - mpmath=1.3.0
  - msys2-conda-epoch=20160418
  - opencl-headers=2025.06.13
  - openjpeg=2.5.3
  - openssl=3.5.1
  - pip=25.1.1
  - pthread-stubs=0.4
  - pthreads-win32=2.9.1
  - pycparser=2.22
  - pysocks=1.7.1
  - python=3.9.23
  - python_abi=3.9
  - pytorch=2.5.1
  - pytorch-cuda=12.4
  - pytorch-mutex=1.0
  - pyyaml=6.0.2
  - tbb=2021.13.0
  - tk=8.6.13
  - ucrt=10.0.22621.0
  - vc=14.3
  - vc14_runtime=14.44.35208
  - vs2015_runtime=14.44.35208
  - webrtcvad=2.0.10
  - wheel=0.45.1
  - win_inet_pton=1.1.0
  - xorg-libxau=1.0.11
  - xorg-libxdmcp=1.1.3
  - yaml=0.2.5
  - zstandard=0.23.0
  - zstd=1.5.7
  - pip:
      - absl-py==2.3.1
      - accelerate==0.23.0
      - addict==2.4.0
      - aiohappyeyeballs==2.6.1
      - aiohttp==3.12.14
      - aiosignal==1.4.0
      - albumentations==1.3.0
      - annotated-types==0.7.0
      - antlr4-python3-runtime==4.8
      - anyascii==0.3.3
      - async-timeout==5.0.1
      - attrs==25.3.0
      - audioread==3.0.1
      - av==12.3.0
      - babel==2.17.0
      - bangla==0.0.5
      - basicsr==1.4.2
      - blinker==1.9.0
      - blis==1.2.0
      - bnnumerizer==0.0.2
      - bnunicodenormalizer==0.1.7
      - catalogue==2.0.10
      - certifi==2024.8.30
      - charset-normalizer==3.3.2
      - clean-fid==0.1.35
      - click==8.1.7
      - clip-anytorch==2.6.0
      - cloudpathlib==0.22.0
      - colorama==0.4.6
      - coloredlogs==15.0.1
      - compel==2.0.1
      - comtypes==1.4.7
      - confection==0.1.5
      - contourpy==1.2.1
      - controlnet-aux==0.0.6
      - coqpit==0.0.17
      - ctranslate2==4.6.0
      - cycler==0.12.1
      - cymem==2.0.11
      - cython==3.1.4
      - dateparser==1.1.8
      - decorator==5.2.1
      - diffusers==0.28.2
      - diskcache==5.6.3
      - docopt==0.6.2
      - einops==0.8.1
      - emoji==2.8.0
      - encodec==0.1.1
      - eval-type-backport==0.2.2
      - facexlib==0.3.0
      - faster-whisper==1.0.3
      - filelock==3.16.0
      - filterpy==1.4.5
      - flask==3.1.2
      - flatbuffers==24.3.25
      - fonttools==4.58.5
      - frozenlist==1.7.0
      - fsspec==2024.9.0
      - ftfy==6.3.1
      - future==1.0.0
      - g2pkk==0.1.2
      - gfpgan==1.3.8
      - gitdb==4.0.12
      - gitpython==3.1.44
      - grpcio==1.73.1
      - gruut==2.2.3
      - gruut-ipa==0.13.0
      - gruut-lang-de==2.0.1
      - gruut-lang-en==2.0.1
      - gruut-lang-es==2.0.1
      - gruut-lang-fr==2.0.2
      - halo==0.0.31
      - hangul-romanize==0.1.0
      - huggingface-hub==0.24.6
      - humanfriendly==10.0
      - idna==3.8
      - imageio==2.37.0
      - importlib-metadata==8.7.0
      - importlib-resources==6.5.2
      - inflect==7.5.0
      - invisible-watermark==0.2.0
      - itsdangerous==2.2.0
      - jamo==0.4.1
      - jieba==0.42.1
      - jinja2==3.1.4
      - joblib==1.4.2
      - jsonlines==1.2.0
      - jsonmerge==1.9.2
      - jsonschema==4.24.0
      - jsonschema-specifications==2025.4.1
      - k-diffusion==0.0.12
      - kiwisolver==1.4.7
      - kornia==0.6.0
      - langcodes==3.5.0
      - language-data==1.3.0
      - lazy-loader==0.4
      - librosa==0.10.0
      - llama-cpp-python==0.3.16
      - llvmlite==0.43.0
      - lmdb==1.7.2
      - log-symbols==0.0.14
      - marisa-trie==1.3.1
      - markdown==3.8.2
      - markdown-it-py==3.0.0
      - markupsafe==2.1.5
      - matplotlib==3.8.4
      - mdurl==0.1.2
      - more-itertools==10.8.0
      - msgpack==1.1.1
      - multidict==6.6.3
      - murmurhash==1.0.13
      - networkx==2.8.8
      - nltk==3.8.1
      - num2words==0.5.14
      - numba==0.60.0
      - numpy==1.22.0
      - omegaconf==2.1.1
      - onnxruntime==1.19.2
      - open-clip-torch==2.0.2
      - opencv-python==********
      - opencv-python-headless==*********
      - openwakeword==0.6.0
      - packaging==24.1
      - pandas==1.5.3
      - picklescan==0.0.26
      - piexif==1.1.3
      - pillow==10.2.0
      - platformdirs==4.3.8
      - pooch==1.8.2
      - preshed==3.0.10
      - propcache==0.3.2
      - protobuf==5.28.1
      - psutil==6.0.0
      - pvporcupine==1.9.5
      - pyaudio==0.2.14
      - pydantic==2.11.7
      - pydantic-core==2.33.2
      - pydeprecate==0.3.1
      - pydub==0.25.1
      - pygments==2.19.2
      - pynndescent==0.5.13
      - pyparsing==3.2.3
      - pypinyin==0.55.0
      - pypiwin32==223
      - pyreadline3==3.5.2
      - pysbd==0.3.4
      - python-crfsuite==0.9.11
      - python-dateutil==2.9.0.post0
      - pytorch-lightning==1.4.2
      - pytz==2025.2
      - pywavelets==1.6.0
      - pywin32==306
      - qudida==0.0.4
      - realesrgan==0.3.0
      - realtimestt==0.2.41
      - realtimetts==0.4.5
      - referencing==0.36.2
      - regex==2024.7.24
      - requests==2.32.3
      - resize-right==0.0.2
      - rich==14.1.0
      - rpds-py==0.26.0
      - safetensors==0.4.5
      - scikit-image==0.24.0
      - scikit-learn==1.5.2
      - scipy==1.11.4
      - sentencepiece==0.2.0
      - sentry-sdk==2.32.0
      - setuptools==69.5.1
      - shellingham==1.5.4
      - six==1.16.0
      - smart-open==7.3.1
      - smmap==5.0.2
      - soundfile==0.13.1
      - soxr==1.0.0
      - spacy==3.8.7
      - spacy-legacy==3.0.12
      - spacy-loggers==1.0.5
      - spinners==0.0.24
      - srsly==2.5.1
      - srt==3.5.3
      - stanza==1.6.1
      - stream2sentence==0.2.5
      - sudachidict-core==20250825
      - sudachipy==0.6.10
      - sympy==1.13.1
      - tb-nightly==2.20.0a20250711
      - tensorboard==2.19.0
      - tensorboard-data-server==0.7.2
      - termcolor==2.4.0
      - test-tube==0.7.5
      - thinc==8.3.4
      - threadpoolctl==3.5.0
      - tifffile==2024.8.30
      - timm==1.0.17
      - tokenizers==0.19.1
      - tomli==2.2.1
      - torchaudio==2.5.1
      - torchdiffeq==0.2.5
      - torchmetrics==0.6.0
      - torchruntime==1.20.0
      - torchsde==0.2.6
      - torchvision==0.20.1
      - tqdm==4.66.5
      - trainer==0.0.36
      - trampoline==0.1.2
      - transformers==4.44.2
      - tts==0.22.0
      - typeguard==4.4.4
      - typer==0.19.2
      - typing-extensions==4.15.0
      - typing-inspection==0.4.1
      - tzdata==2025.2
      - tzlocal==5.3.1
      - umap-learn==0.5.7
      - unidecode==1.4.0
      - urllib3==2.2.2
      - wandb==0.21.0
      - wasabi==1.1.3
      - wcwidth==0.2.13
      - weasel==0.4.1
      - webrtcvad-wheels==2.0.14
      - websockets==12.0
      - werkzeug==3.1.3
      - wrapt==1.17.3
      - yapf==0.43.0
      - yarl==1.20.1
      - zipp==3.23.0
prefix: C:\ProgramData\miniforge3\envs\rachael
