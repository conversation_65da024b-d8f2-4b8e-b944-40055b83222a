- Use cloned voice

- Skip voicing code files
- Integrate MultiLLM to create and maintain code projects
- Use small LLM by default, and ask it to route complex queries to large LLM
- Voice command pipeline

- Stop AI speech when user speaks
- Listen while AI is thinking or speaking (separate threads)
- Hold multiple conversations at once
- Manage tasks

- Handle CTRL+C to stop the program
- Handle context overflow errors
- Python profiler (decorator)
- Create executable bundles with Llamafile
- Multi-lingual models
- Vision models
